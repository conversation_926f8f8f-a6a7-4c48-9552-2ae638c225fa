import React from "react"
// import Video from "../../../Tools/Host/video";
// import MP from "../../MatterPort/Host/MP"
import { connect } from "react-redux";
import * as HostActions from "../../../Actions/HostAction";
import { socket } from "../../../Actions/HostAction"
// import Embed3d from "../../Embed3d/Host/Scene";
import * as ExceptionActions from "../../../Actions/Exception"
import Timer from "../../../Tools/ToolComponents/Timer";
import { PostRequestWithHeaders } from "../../../Tools/helpers/api";
import Ale from "../../Ale/Host/Scene"
import PixelStreaming from "../../PixelStreaming/Host/Pixel"
import Lark from "../../LarkXr/Host/lark"
import { CheckAvailabilityApi, ExtendSessionApi, UpdateSessionApi } from "../../../Tools/helpers/BackendApis";
import LoaderOverlay from "../../../components/LoaderOverlay";
import { Firebase } from "../../../config/Firebase";
import * as Sessions from "../../../Actions/Sessions";

class TypeSwitch extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      interval: 30,
      isSessionStarted: false,
      isSessionEnded: false,
      isNearingEnd: false,
      extendDuration: 30,
      isExtending: false,
      isSlotAvailable: false,
      checkingAvailability: false,
      hasCheckedAvailability: false,
      isPixel:false,
      hasInitialCheck: false,
      isUpdatingSession: false, // Add this flag
      lastUpdateAttempt: null   // Track last update attempt
      // Pin state is now managed by parent component
    };
    this.SetDuration = this.SetDuration.bind(this);
    this.handleExtendSession = this.handleExtendSession.bind(this);
    this.handleLeaveSession = this.handleLeaveSession.bind(this);
    this.checkAvailability = this.checkAvailability.bind(this);
    this.togglePin = this.togglePin.bind(this);


    window.addEventListener('beforeunload', (e) => {
      e.preventDefault();
      if (this.props.LastUpdate) {
         clearInterval(this.job);
          this.SetDuration(this.state.interval)
      }

      socket.emit('BE-leave-room', { roomId: this.props.roomId, leaver: this.props.UserName });
      sessionStorage.removeItem('user');
      window.location.href = '/';
    });
  }


  async checkAvailability() {
    if (this.state.hasCheckedAvailability) return;

    this.setState({ checkingAvailability: true, isSlotAvailable: false });
    try {
      const availabilityConfig = {
        organization_id: this.props.SessionDetails.organization_id,
        project_id: this.props.SessionDetails.project_id,
        start_time: new Date(),
        end_time: new Date(Date.now() + this.state.extendDuration * 60000),
        maxConcurrentSessions: 1
      };

      const availabilityResponse = await CheckAvailabilityApi(availabilityConfig);
      this.setState({
        isSlotAvailable: availabilityResponse.availableSessions > 0,
        checkingAvailability: false,
        hasCheckedAvailability: true,
        isPixel:true,
      });
    } catch (error) {
      console.error("Failed to check availability:", error);
      this.setState({
        isSlotAvailable: false,
        checkingAvailability: false,
        hasCheckedAvailability: true,
        isPixel:false,
      });
    }
  }

  async handleExtendSession() {
    this.setState({ isExtending: true });
    try {
      const extendResponse = await ExtendSessionApi(this.props.roomId, this.state.extendDuration);

      if (extendResponse) {
        this.props.CreateToast({ message: "Session extended successfully" });
        this.setState({
          isSessionEnded: false,
          isNearingEnd: false,
          isSlotAvailable: false,
          hasCheckedAvailability: false, // Reset this when extending the session
          isPixel:true,
        });
      }
    } catch (error) {
      console.error("Failed to extend session:", error);
      this.props.CreateToast({message:"Failed to extend session"});
    } finally {
      this.setState({ isExtending: false });
    }
  }

  handleLeaveSession() {
    window.location.href = '/sessions';
  }

  
updateSessionToDefault = async () => {
  // Prevent multiple simultaneous updates
  if (this.state.isUpdatingSession) {
    return;
  }

  // Prevent too frequent updates (minimum 5 seconds between attempts)
  const now = Date.now();
  if (this.state.lastUpdateAttempt && (now - this.state.lastUpdateAttempt) < 5000) {
    return;
  }

  this.setState({ isUpdatingSession: true, lastUpdateAttempt: now });

  try {
    // console.log("Starting updateSessionToDefault...");
    
    // Update Redux store
    const updatedSessionDetails = {
      ...this.props.SessionDetails,
      type: 'default'
    };
    
    this.props.SetSessions(updatedSessionDetails);

    // Update Firestore session document
    console.log("Updating Firestore document for roomId:", this.props.roomId);
    await Firebase.firestore().collection('sessions').doc(this.props.roomId).update({
      type: 'default',
      project_id: null,
    });
    
    const apiResponse = await UpdateSessionApi(
      this.props.roomId,
      this.props.SessionDetails?.project_id || null,
      'default', // Set type to default
      false, // Not pixel streaming
      0 // Duration
    );

  } catch (error) {
    // console.error("Error updating session type to default:", error);
    // console.error("Error details:", {
    //   message: error.message,
    //   stack: error.stack,
    //   roomId: this.props.roomId,
    //   sessionDetails: this.props.SessionDetails
    // });
    
    // Optionally show user-facing error
    if (this.props.CreateToast) {
      this.props.CreateToast({
        message: "Failed to update session",
        postmessage: "Please refresh the page if issues persist"
      });
    }
  } finally {
    this.setState({ isUpdatingSession: false });
  }
};


componentDidMount() {
  this.sessionCheckInterval = setInterval(() => {
    const currentTime = new Date().getTime();
    // Use schedule_time if available, otherwise use start time
    const startTime = new Date(this.props.SessionDetails.schedule_time || this.props.SessionDetails.start).getTime();
    const endTime = new Date(this.props.SessionDetails.end_time).getTime();
    
    // console.log("Time Check:", {
    //   current: new Date(currentTime).toISOString(),
    //   start: new Date(startTime).toISOString(),
    //   end: new Date(endTime).toISOString(),
    //   isScheduled: !!this.props.SessionDetails.schedule_time,
    //   hasStarted: currentTime >= startTime,
    //   hasEnded: currentTime >= endTime
    // });

    const isSessionStarted = currentTime >= startTime && currentTime < endTime;
    const isSessionEnded = currentTime >= endTime;
    const isSessionNotStarted = currentTime < startTime;
    
    this.setState({
      isSessionStarted,
      isSessionEnded,
      isNearingEnd: this.checkNearingEnd(this.props.SessionDetails.end_time),
      isPixel: isSessionStarted && !isSessionEnded
    });

    // Only update to default for non-default and non-ale sessions
    const currentType = this.props.SessionDetails?.type;
    const shouldUpdateToDefault = currentType && 
                                 currentType !== 'default' && 
                                 currentType !== 'ale' &&
                                 (isSessionEnded || isSessionNotStarted);

    if (shouldUpdateToDefault) {
      console.log("Session is not active (ended or not started) - updating type to default");
      console.log("Current session type:", currentType);
      console.log("Session state:", { isSessionEnded, isSessionNotStarted, isSessionStarted });
      this.updateSessionToDefault();
    }

    // Initial check logic (only run once)
    if (!this.state.hasInitialCheck) {
      this.setState({ hasInitialCheck: true });
      
      if (currentType && currentType !== 'default' && currentType !== 'ale' && !isSessionStarted) {
        console.log("Initial check - session not active, setting type to default");
        this.updateSessionToDefault();
      }
    }

    // Check availability when session ends
    if (isSessionEnded && !this.state.hasCheckedAvailability) {
      this.checkAvailability();
    }
  }, 1000);
}

  componentWillUnmount() {
    clearInterval(this.job);
    clearInterval(this.sessionCheckInterval);
  }

  // componentDidUpdate(prevProps, prevState) {
  //   if (!prevState.isSessionEnded && this.state.isSessionEnded) {
  //     this.checkAvailability();
  //   }
  // }

  checkNearingEnd(endTimestamp) {
    const endTime = new Date(endTimestamp);
    const currentTime = new Date();
    const timeDifference = endTime.getTime() - currentTime.getTime();
    const fiveMinutesInMs = 5 * 60 * 1000;
    return timeDifference <= fiveMinutesInMs && timeDifference > 0;
  }
SetDuration(duration) {
    console.log("SetDuration", this.state.isPixel);
  console.log("duration", duration)
    // Get the current session type and project ID
    const sessionType = this.props.SessionDetails?.type || 'default';
    const projectId = this.props.SessionDetails?.project_id || null;

    // Determine if pixel streaming is active based on session type
    const isPixelActive = this.state.isPixel ||
                         sessionType === 'pixel_streaming' ||
                         sessionType === 'lark';


    console.log("Duration minutes:",  duration/60,); // Debug log

    // Call the UpdateSession API with duration_minutes
    UpdateSessionApi(
        this.props.roomId,
        projectId,
        sessionType,
        isPixelActive,
        duration/60 // Use the validated duration
    ).then(response => {
        console.log("UpdateSession response:", response);
    }).catch(error => {
        console.error("Error updating session:", error);
    });
}
  toHumanReadableTime(isoString) {
    const date = new Date(isoString);

    const formattedDate = `${date.toLocaleTimeString()} on ${date.toDateString()}`;

    return formattedDate;
  }

    CheckStartTiming (timestamp, endTimestamp) {
    const currentTime = new Date();
    // Use schedule_time if available, otherwise use start time
    const startTime = new Date(this.props.SessionDetails.schedule_time || this.props.SessionDetails.start);
    const endTime = new Date(endTimestamp);

    console.log("Start Check:", {
      startTime: startTime.toISOString(),
      currentTime: currentTime.toISOString(),
      endTime: endTime.toISOString()
    });

    if (startTime <= currentTime && currentTime < endTime) {
      return true;
    }
    return false;
  }

  checkEndTimimg (timestamp, endTimestamp) {
    const currtime = new Date();
    const endTime = new Date(endTimestamp);
    
    console.log("End Check:", {
      current: currtime.toISOString(),
      end: endTime.toISOString(),
      hasEnded: currtime >= endTime
    });

    if (currtime >= endTime) {
      this.setState({ 
        isSessionStarted: false,
        isSessionEnded: true,
        isPixel: false 
      });
      return true;
    }
    return false;
  }

  togglePin() {
    // Use parent component's toggle function
    if (this.props.onTogglePin) {
      this.props.onTogglePin();
    }
  }

  render() {

    // Check if project ID is undefined or type is 'default'
    const isDefaultOrNoProject = !this.props.SessionDetails?.project_id ||
                                 this.props.SessionDetails?.type === 'default';

    if (this.props.SessionDetails) {
      // Check if both screen sharing and project are active
      const hasProject = !isDefaultOrNoProject;
      const isHostSharing = this.props.userVideoAudio && this.props.userVideoAudio.localUser &&
                            this.props.userVideoAudio.localUser.screen;
      const sharingPeer = this.props.Peers && Object.values(this.props.Peers).find(peer =>
        peer && peer.peerID && this.props.userVideoAudio && this.props.userVideoAudio[peer.peerID] &&
        this.props.userVideoAudio[peer.peerID].screen
      );
      const hasScreenSharing = !!(isHostSharing || sharingPeer);

      // Determine what content to show based on pin state
      const showPinIcon = hasProject && hasScreenSharing;
      const isScreenSharingPinned = this.props.isScreenSharingPinned || false;
      let showScreenSharing = hasScreenSharing;
      let showProject = hasProject;

      if (showPinIcon) {
          // When both are active, show based on pinned content
          // Default: Project content in main area, screen sharing in participants area
          // When pinned: Screen sharing in main area, project content hidden
          showScreenSharing = isScreenSharingPinned; // Show screen sharing in main area only when pinned
          showProject = !isScreenSharingPinned; // Show project in main area only when screen sharing is not pinned
      }

      // If screen sharing should be shown
      if (showScreenSharing && (isHostSharing || sharingPeer)) {
        const sharingUserName = isHostSharing ? 'You' : sharingPeer.userName;

        return (
          <div className="w-full h-full relative bg-black">
            {/* Pin icon - show when both project and screen sharing are active */}
            {showPinIcon && (
                <button
                    onClick={this.togglePin}
                    className="absolute top-4 right-4 z-[9999990] bg-black/50 hover:bg-black/70 text-white p-2 rounded-lg transition-colors"
                    title={`${isScreenSharingPinned ? 'Unpin screen sharing to participants' : 'Pin screen sharing to main area'}`}
                >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z" />
                    </svg>
                </button>
            )}

            {/* Screen sharing content */}
            <div className="absolute inset-0">
              {isHostSharing ? (
                <video
                  className="w-full h-full object-contain bg-black ScreenSharingInTypeSwitch"
                  id="HOST_SCREEN_VIDEO"
                  autoPlay
                  muted
                  playsInline
                  style={{ width: '100%', height: '100%' }}
                  ref={(video) => {
                    if (video && this.props.LocalStream && !video.srcObject) {
                      video.srcObject = this.props.LocalStream;
                    }
                  }}
                />
              ) : (
                <video
                  className="w-full h-full object-cover bg-black ScreenSharingInTypeSwitch"
                  id={sharingPeer.peerID + "SCREEN_VIDEO"}
                  autoPlay
                  playsInline
                  style={{ width: '100%', height: '100%' }}
                  ref={(video) => {
                    if (video && sharingPeer.streams && sharingPeer.streams[0] && !video.srcObject) {
                      video.srcObject = sharingPeer.streams[0];
                    }
                  }}
                />
              )}
              {/* User name overlay */}
              <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded">
                {sharingUserName} {isHostSharing ? 'are' : 'is'} sharing {isHostSharing ? 'your' : 'their'} screen
              </div>
            </div>
          </div>
        );
      }

      // If no screen sharing and project ID is undefined or type is 'default', show default video call interface
      if (isDefaultOrNoProject) {
        return (
          <div className="w-full h-full">
            {/* The VideoCard components in SideBar will handle screen sharing display automatically */}
          </div>
        );
      }

      // Otherwise, render based on the project type
      if (showProject) {
        switch (this.props.SessionDetails.type) {
            case "pixel_streaming": // Handle regular pixel streaming type
              return (
                <div className="w-full h-full relative">
                  {/* Pin functionality is handled in VideoCard components in participants area */}

                  {this.state.isSessionStarted ? (
                  <PixelStreaming
                    SendCustomMessage={this.props.SendCustomMessage}
                    SetMessage={this.props.SetMessage}
                    roomId={this.props.roomId}
                    isNearingEnd={this.state.isNearingEnd}
                  />
                ) : (
                <div className="fixed left-1/2 transform -translate-x-1/2 top-1/3 z-30 flex items-center justify-center">
                  <div className="bg-[black]/40 bg-opacity-40 backdrop-blur p-6 rounded-lg shadow-md text-center">
                    <h2 className="text-lg font-bold text-white mb-3">
                      {this.state.isSessionEnded ? "Session is Over" : "Session Not Started Yet"}
                    </h2>
                    <p className="text-base text-white mb-3">
                      {this.state.isSessionEnded
                        ? "You're welcome to stay on the call, though the full experience may not be available at this time."
                        : "The session has not begun. Please wait until the scheduled start time."}
                    </p>
                    {!this.state.isSessionEnded && (
                      <p className="text-md text-white mb-3">
                        Scheduled Start Time: {this.toHumanReadableTime(this.props.SessionDetails.schedule_time)}
                      </p>
                    )}
                    {this.state.isSessionEnded && (
                      <div className="flex justify-center space-x-4">
                        <button
                          onClick={this.handleExtendSession}
                          disabled={this.state.isExtending || this.state.checkingAvailability || !this.state.isSlotAvailable}
                          className={`bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded ${(this.state.isExtending || this.state.checkingAvailability || !this.state.isSlotAvailable) ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          {this.state.isExtending ? 'Extending...' :
                           this.state.checkingAvailability ? 'Checking availability...' :
                           this.state.hasCheckedAvailability ? (this.state.isSlotAvailable ? 'Extend Session (30 mins)' : 'No slots available') :
                           'Extend Session (30 mins)'}
                        </button>
                        <button
                          onClick={this.handleLeaveSession}
                          disabled={this.state.isExtending}
                          className={`bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded ${this.state.isExtending ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          Leave Session
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}
              </div>
            );
          case "lark": // Handle LarkXR type
            return (
              <div className="w-full relative" style={{ height: this.props.ShowControls === false ? '100vh' : '100%' }}>
                {/* Pin icon - show when both project and screen sharing are active */}
                {showPinIcon && (
                    <button
                        onClick={this.togglePin}
                        className="absolute top-4 right-4 z-[9999990] bg-black/50 hover:bg-black/70 text-white p-2 rounded-lg transition-colors"
                        title={`${isScreenSharingPinned ? 'Unpin screen sharing to participants' : 'Pin screen sharing to main area'}`}
                    >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z" />
                        </svg>
                    </button>
                )}

                {this.state.isSessionStarted ? (
                <Lark
                  SendCustomMessage={this.props.SendCustomMessage}
                  SetMessage={this.props.SetMessage}
                  roomId={this.props.roomId}
                  isNearingEnd={this.state.isNearingEnd}
                />
              ) : (
                <div className="fixed left-1/2 transform -translate-x-1/2 top-1/3 z-30 flex items-center justify-center">
                  <div className="bg-[black]/40 bg-opacity-40 backdrop-blur p-6 rounded-lg shadow-md text-center">
                    <h2 className="text-lg font-bold text-white mb-3">
                      {this.state.isSessionEnded ? "Session is Over" : "Session Not Started Yet"}
                    </h2>
                    <p className="text-base text-white mb-3">
                      {this.state.isSessionEnded
                        ? "You're welcome to stay on the call, though the full experience may not be available at this time."
                        : "The session has not begun. Please wait until the scheduled start time."}
                    </p>
                    {!this.state.isSessionEnded && (
                      <p className="text-md text-white mb-3">
                        Scheduled Start Time: {this.toHumanReadableTime(this.props.SessionDetails.schedule_time)}
                      </p>
                    )}
                    {/* {this.state.isSessionEnded && (
                      <div className="flex justify-center space-x-4">
                        <button
                          onClick={this.handleExtendSession}
                          disabled={this.state.isExtending || this.state.checkingAvailability || !this.state.isSlotAvailable}
                          className={`bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded ${(this.state.isExtending || this.state.checkingAvailability || !this.state.isSlotAvailable) ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          {this.state.isExtending ? 'Extending...' :
                           this.state.checkingAvailability ? 'Checking availability...' :
                           this.state.hasCheckedAvailability ? (this.state.isSlotAvailable ? 'Extend Session (30 mins)' : 'No slots available') :
                           'Extend Session (30 mins)'}
                        </button>
                        <button
                          onClick={this.handleLeaveSession}
                          disabled={this.state.isExtending}
                          className={`bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded ${this.state.isExtending ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          Leave Session
                        </button>
                      </div>
                    )} */}
                  </div>
                </div>
              )}
              </div>
            );
        case "ale":
        return (
          <div className="w-full h-full relative object-contain">
            {/* Pin icon - show when both project and screen sharing are active */}
            {showPinIcon && (
                <button
                    onClick={this.togglePin}
                    className="absolute top-4 right-4 z-[9999990] bg-black/50 hover:bg-black/70 text-white p-2 rounded-lg transition-colors"
                    title={`${isScreenSharingPinned ? 'Unpin screen sharing to participants' : 'Pin screen sharing to main area'}`}
                >
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z" />
                    </svg>
                </button>
            )}

            <Ale roomId={this.props.roomId} SendCustomMessage={this.props.SendCustomMessage}/>
          </div>
          );
        default:
          return <></>;
        }
      }

      // If no content should be shown, return empty
      return <></>;
    }
    else {
      return (
        <>
          <div>Loading...</div>
        </>
      )
    }
  }
}


const mapStateTothisprops = state => {
  return {
    Config: state.Call.config,
    userscount: state.Call.userscount,
    Plan: state.Auth.plan,
    Auth: state.Auth.auth,
    UserName: state.Call.UserName,
    LastUpdate:state.Call.LastUpdate,
    ProjectDetails: state.Sessions.projectDetails,
    SessionDetails: state.Sessions.sessions,
    SessionConfig: state.Sessions.configData,
    Peers: state.Call.peers,
    LocalStream: state.Call.LocalStream,
    userVideoAudio: state.Call.userVideoAudio,

  }
}

const mapDispatchTothisprops = {
  ...HostActions,
  ...ExceptionActions,
    ...Sessions
}

export default connect(mapStateTothisprops, mapDispatchTothisprops)(TypeSwitch)