import React from "react"
import * as ExceptionActions from "../../../Actions/Exception"
import { connect } from "react-redux"
import * as Sessions from '../../../Actions/Sessions';
import axios from "axios";
import Fire from "../../../config/Firebase.jsx";
import CloudStreamingView from "../../../components/CloudStreaming/CloudStreamingView.jsx";

class Lark extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            hasShownEndingToast: false,
            sdkConnected: false,
            authCode: null,
            sdkId: null,
            isGettingAuthCode: false,
            isProjectSwitching: false,
            currentApplicationId: null,
            hostReady: false
        };
        this.getAuthCode = this.getAuthCode.bind(this);
        this.handleSDKConnectionSuccess = this.handleSDKConnectionSuccess.bind(this);
        this.handleSDKConnectionError = this.handleSDKConnectionError.bind(this);
        this.clearHostCredentials = this.clearHostCredentials.bind(this);
    }

    async getAuthCode(forceNew = false) {
        // Allow getting new auth code if forceNew is true (for project switching)
        if (this.state.isGettingAuthCode || (!forceNew && this.state.authCode)) {
            console.log("Host: Skipping auth code request - already getting or have code", {
                isGettingAuthCode: this.state.isGettingAuthCode,
                hasAuthCode: !!this.state.authCode,
                forceNew
            });
            return;
        }

        console.log("Host: Getting auth code...", { forceNew });
        this.setState({
            isGettingAuthCode: true,
            hostReady: false // Reset ready state when getting new auth code
        });

        try {
            const response = await axios.get('https://ps.propvr.io:8181/util/getAuthCode');
            console.log("Host: Auth code response:", response.data);

            if (response.data && response.data.message) {
                this.setState({
                    authCode: response.data.message,
                    isGettingAuthCode: false,
                    currentApplicationId: this.props.ProjectDetails?.projectSettings?.pixelstreaming?.application_id
                });
                console.log("Host: Auth code set:", response.data.message);
                console.log("Host: Auth code length:", response.data.message.length);
            } else {
                throw new Error("Invalid auth code response");
            }
        } catch (error) {
            console.error("Host: Failed to get auth code:", error);
            this.setState({
                isGettingAuthCode: false,
                authCode: null,
                hostReady: false
            });
            this.props.CreateToast({
                message: "Authentication Error",
                postmessage: "Failed to get authentication code."
            });
        }
    }

    // Handle SDK connection success - this is called when LarkXR 'connect' event fires
    handleSDKConnectionSuccess(sdkId) {
        console.log("Host: LarkXR 'connect' event received - Host is truly connected and ready!");
        console.log("Host: SDK ID:", sdkId);

        this.setState({
            sdkConnected: true,
            sdkId: sdkId,
            hostReady: true, // Set ready immediately when LarkXR connect event fires
            isProjectSwitching: false
        });

        // Add a small delay before storing credentials to ensure LarkXR session is fully established
        setTimeout(() => {
            console.log("Host: Storing credentials for guests after stabilization delay");
            this.storeSessionCredentials(sdkId, this.state.authCode);
        }, 500);

        // this.props.CreateToast({
        //     message: "LarkXR Connected",
        //     postmessage: "Successfully connected to the streaming service."
        // });
    }

    // Add this new method to verify roomId
    verifyRoomId() {
        console.log('Host: verifyRoomId', this.props.roomId, this.props.sessionState);
        if (!this.props.roomId && this.props.sessionState) {
            // Try to get roomId from sessionState
            const roomId = this.props.sessionState._id;
            if (roomId) {
                console.log("Host: Found roomId from sessionState:", roomId);
                return roomId;
            }
        }
        return this.props.roomId;
    }

    // Clear host credentials from Firestore (for project switching)
    async clearHostCredentials() {
        const roomId = this.verifyRoomId();

        if (!roomId) {
            console.error("Host: Cannot clear credentials - roomId is missing");
            return;
        }

        console.log("Host: Clearing credentials for project switch...");

        try {
            await Fire.firestore()
                .collection('sessions')
                .doc(roomId)
                .collection('config')
                .doc('data')
                .set({
                    hostConnected: false,
                    hostReady: false,
                    projectSwitching: true,
                    timestamp: new Date()
                }, { merge: true });

            console.log("Host: Successfully cleared credentials");
        } catch (error) {
            console.error("Host: Failed to clear credentials:", error);
        }
    }

    // Store session credentials in Firestore for guests
    storeSessionCredentials(sdkId, authCode) {
        const roomId = this.verifyRoomId();

        console.log("Host: storeSessionCredentials called with:", {
            roomId,
            sdkId,
            authCode,
            applicationId: this.props.ProjectDetails?.projectSettings?.pixelstreaming?.application_id,
            hostReady: this.state.hostReady,
            sdkConnected: this.state.sdkConnected,
            fullSessionState: this.props.sessionState
        });

        // CRITICAL: Only store credentials if host is truly ready (LarkXR 'connect' event fired)
        if (!this.state.hostReady) {
            console.warn("Host: Refusing to store credentials - host not ready yet!");
            console.warn("Host: This should only be called after LarkXR 'connect' event fires");
            return;
        }

        if (!roomId) {
            console.error("Host: Cannot store credentials - roomId is missing");
            // this.props.CreateToast({
            //     message: "Session Error",
            //     postmessage: "Failed to store session credentials - missing room ID"
            // });
            return;
        }
        if (!sdkId) {
            console.error("Host: Cannot store credentials - sdkId is missing");
            return;
        }
        if (!authCode) {
            console.error("Host: Cannot store credentials - authCode is missing");
            return;
        }

        const applicationId = this.props.ProjectDetails?.projectSettings?.pixelstreaming?.application_id;
        if (!applicationId) {
            console.error("Host: Cannot store credentials - applicationId is missing");
            return;
        }

        const credentialsData = {
            authCode: authCode,
            applicationId: applicationId,
            hostConnected: true,
            hostReady: this.state.hostReady, // This is true only after LarkXR 'connect' event
            larkXRConnected: this.state.sdkConnected, // Additional flag for LarkXR connection status
            projectSwitching: false,
            timestamp: new Date()
        };

        console.log("Host: Storing credentials data:", credentialsData);
        console.log("Host: Call stack for storeSessionCredentials:", new Error().stack);

        Fire.firestore()
            .collection('sessions')
            .doc(roomId)
            .collection('config')
            .doc('data')
            .set(credentialsData, { merge: true })
            .then(() => {
                console.log("Host: Successfully stored session credentials");
                console.log("Host: Guests should now receive:", credentialsData);
            })
            .catch((error) => {
                console.error("Host: Failed to store session credentials:", error);
            });
    }

    // Handle SDK connection error
    handleSDKConnectionError(error) {
        console.error("Host SDK connection error:", error);
        this.setState({ sdkConnected: false });

        // this.props.CreateToast({
        //     message: "Connection Error",
        //     postmessage: "Failed to connect to LarkXR. Please check your configuration."
        // });
    }

    componentDidMount() {
        console.log("Host: LarkXR component mounted with:", {
            ProjectDetails: this.props.ProjectDetails,
            roomId: this.props.roomId,
            sessionState: this.props.sessionState
        });

        // Add Firestore interceptor to catch unauthorized credential storage
        this.setupFirestoreInterceptor();

        // Delete any existing credentials to ensure clean state
        this.deleteHostCredentials();

        this.getAuthCode();
    }

    // Setup Firestore interceptor to catch unauthorized credential storage
    setupFirestoreInterceptor() {
        const roomId = this.verifyRoomId();
        if (!roomId) return;

        console.log("Host: Setting up Firestore interceptor to catch unauthorized credential storage");

        // Intercept Firestore set operations
        const originalSet = Fire.firestore().collection('sessions').doc(roomId).collection('config').doc('data').set;
        const self = this;

        Fire.firestore().collection('sessions').doc(roomId).collection('config').doc('data').set = function(data, options) {
            if (data && (data.authCode || data.applicationId)) {
                console.warn("🚨 FIRESTORE INTERCEPTOR: Someone is trying to store credentials!");
                console.warn("🚨 Data being stored:", data);
                console.warn("🚨 Host ready status:", self.state.hostReady);
                console.warn("🚨 Call stack:", new Error().stack);

                if (!self.state.hostReady && data.authCode && data.applicationId) {
                    console.error("🚨 BLOCKING: Attempt to store credentials before host is ready!");
                    return Promise.resolve(); // Block the operation
                }
            }

            return originalSet.call(this, data, options);
        };
    }

    // Delete host credentials completely (for clean initialization)
    async deleteHostCredentials() {
        const roomId = this.verifyRoomId();

        if (!roomId) {
            console.error("Host: Cannot delete credentials - roomId is missing");
            return;
        }

        console.log("Host: Deleting any existing credentials for clean initialization...");

        try {
            await Fire.firestore()
                .collection('sessions')
                .doc(roomId)
                .collection('config')
                .doc('data')
                .delete();

            console.log("Host: Successfully deleted existing credentials");
        } catch (error) {
            console.error("Host: Failed to delete credentials:", error);
        }
    }

    componentDidUpdate(prevProps) {
        // Handle session ending notification
        if (this.props.isNearingEnd && !prevProps.isNearingEnd && !this.state.hasShownEndingToast) {
            this.props.CreateToast({
                message: "Session Ending Soon",
                postmessage: "The session will end in less than 5 minutes."
            });
            this.setState({ hasShownEndingToast: true });
        }

        // Handle project switching - check if application ID changed
        const prevAppId = prevProps.ProjectDetails?.projectSettings?.pixelstreaming?.application_id;
        const currentAppId = this.props.ProjectDetails?.projectSettings?.pixelstreaming?.application_id;

        if (prevAppId && currentAppId && prevAppId !== currentAppId && !this.state.isProjectSwitching) {
            console.log("Host: Project switch detected, reinitializing LarkXR...");
            console.log("Host: Previous app ID:", prevAppId);
            console.log("Host: New app ID:", currentAppId);

            this.handleProjectSwitch();
        }
    }

    // Handle project switching with proper sequencing
    async handleProjectSwitch() {
        if (this.state.isProjectSwitching) {
            console.log("Host: Project switch already in progress, skipping...");
            return;
        }

        console.log("Host: Starting project switch sequence...");

        this.setState({
            isProjectSwitching: true,
            hostReady: false
        });

        try {
            // Step 1: Clear old credentials to signal guests to wait
            await this.clearHostCredentials();

            // Step 2: Reset state and get new auth code
            this.setState({
                authCode: null,
                sdkConnected: false,
                sdkId: null
            });

            // Step 3: Get new auth code (force new)
            await this.getAuthCode(true);

        } catch (error) {
            console.error("Host: Error during project switch:", error);
            this.setState({
                isProjectSwitching: false,
                hostReady: false
            });

            this.props.CreateToast({
                message: "Project Switch Error",
                postmessage: "Failed to switch projects. Please try again."
            });
        }
    }
    componentWillUnmount() {
        // Mark host as disconnected when component unmounts
        const roomId = this.verifyRoomId();
        if (roomId) {
            Fire.firestore()
                .collection('sessions')
                .doc(roomId)
                .collection('config')
                .doc('data')
                .set({
                    hostConnected: false,
                    hostReady: false,
                    projectSwitching: false,
                    timestamp: new Date()
                }, { merge: true })
                .catch((error) => {
                    console.error("Host: Failed to mark as disconnected:", error);
                });
        }
    }

    render() {
        // Check if we have the required project details
        if (!this.props.ProjectDetails?.projectSettings?.pixelstreaming?.application_id) {
            return (
                <div style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    top: 0,
                    left: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                }}>
                    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
                    <p className="text-white text-xl font-semibold">Loading Project Configuration...</p>
                    <p className="text-gray-300 mt-2">Please wait while we load the project details</p>
                </div>
            );
        }

        // Check if we're switching projects
        if (this.state.isProjectSwitching) {
            return (
                <div style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    top: 0,
                    left: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                }}>
                    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
                    <p className="text-white text-xl font-semibold">Switching Projects...</p>
                    <p className="text-gray-300 mt-2">Please wait while we switch to the new project</p>
                </div>
            );
        }

        // Check if we're still getting auth code
        if (this.state.isGettingAuthCode) {
            return (
                <div style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    top: 0,
                    left: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                }}>
                    <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
                    <p className="text-white text-xl font-semibold">Getting Authentication...</p>
                    <p className="text-gray-300 mt-2">Please wait while we get your auth code</p>
                </div>
            );
        }

        // Check if we have auth code
        if (!this.state.authCode) {
            return (
                <div style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    top: 0,
                    left: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                }}>
                    <div className="text-red-500 text-6xl mb-4">⚠️</div>
                    <p className="text-white text-xl font-semibold">Authentication Required</p>
                    <p className="text-gray-300 mt-2">Failed to get authentication code</p>
                    <button 
                        onClick={this.getAuthCode}
                        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    >
                        Retry Authentication
                    </button>
                </div>
            );
        }

        // We have everything we need - render the CloudStreamingView
        const applicationId = this.props.ProjectDetails.projectSettings.pixelstreaming.application_id;
        const authCode = this.state.authCode;

        return (
            <CloudStreamingView
                applicationId={applicationId}
                authCode={authCode}
                showControls={this.props.ShowControls}
                onConnectionSuccess={this.handleSDKConnectionSuccess}
                onConnectionError={this.handleSDKConnectionError}
            />
        );
    }
}

const mapStateToProps = state => {
    return {
        ProjectDetails: state.Sessions.projectDetails,
        sessionState: state.Sessions.sessions,
        // Get roomId directly from sessions state
        roomId: state.Sessions.sessions ? state.Sessions.sessions.session_id : null,
        ShowControls: state.Call.ShowControls
    }
}

const mapDispatchToProps = {
    ...ExceptionActions,
    ...Sessions
}

export default connect(mapStateToProps, mapDispatchToProps)(Lark);
